[CoreRedirects]
+PropertyRedirects=(OldName="/Script/WaterAdvanced.ShallowWaterSettings.PhysicsAssetOverridesDataAsset",NewName="/Script/WaterAdvanced.ShallowWaterSettings.PhysicsAssetProxiesDataAsset")
+StructRedirects=(OldName="/Script/WaterAdvanced.ShallowWaterCollisionAgent",NewName="/Script/WaterAdvanced.ShallowWaterCollisionTracker_Actor")
+FunctionRedirects=(OldName="/Script/WaterAdvanced.ShallowWaterSubsystem.AddCollisionAgent",NewName="/Script/WaterAdvanced.ShallowWaterSubsystem.AddCollisionTrackerForActor")
+FunctionRedirects=(OldName="/Script/WaterAdvanced.ShallowWaterSubsystem.RemoveCollisionAgent",NewName="/Script/WaterAdvanced.ShallowWaterSubsystem.RemoveCollisionAgentForActor")
+FunctionRedirects=(OldName="/Script/WaterAdvanced.ShallowWaterSubsystem.RemoveCollisionAgentForActor",NewName="/Script/WaterAdvanced.ShallowWaterSubsystem.RemoveCollisionTrackerForActor")