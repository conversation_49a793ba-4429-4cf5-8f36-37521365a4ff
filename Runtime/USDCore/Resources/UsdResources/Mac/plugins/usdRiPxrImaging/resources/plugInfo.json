{"Plugins": [{"Info": {"Types": {"UsdRiPxrImagingDisplayFilterAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "PxrDisplayFilterPluginBase", "includeDerivedPrimTypes": true}, "UsdRiPxrImagingIntegratorAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "PxrIntegratorPluginBase", "includeDerivedPrimTypes": true}, "UsdRiPxrImagingSampleFilterAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "PxrSampleFilterPluginBase", "includeDerivedPrimTypes": true}}}, "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdRiPxrImaging.dylib", "Name": "usdRiPxrImaging", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}